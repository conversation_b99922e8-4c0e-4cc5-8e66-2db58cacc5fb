// src/components/TestimonialCard.js
import React from 'react';
import styled from 'styled-components';

const CardContainer = styled.div`
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const Thumbnail = styled.img`
  width: 100%;
  height: auto;
`;

const CardContent = styled.div`
  padding: 1rem;
`;

const Name = styled.h3`
  margin: 0 0 0.5rem;
`;

const Quote = styled.p`
  margin: 0 0 1rem;
`;

const TestimonialCard = ({ testimonial }) => {
  // Provide default values if properties are missing
  const { photo = '', name = 'Unknown', quote = '' } = testimonial || {};

  return (
    <CardContainer>
      {photo ? (
        <Thumbnail src={photo} alt={name} />
      ) : (
        <div style={{ padding: '1rem', textAlign: 'center' }}>
          <em>No photo available</em>
        </div>
      )}
      <CardContent>
        <Name>{name}</Name>
        <Quote>{quote}</Quote>
      </CardContent>
    </CardContainer>
  );
};

export default TestimonialCard;