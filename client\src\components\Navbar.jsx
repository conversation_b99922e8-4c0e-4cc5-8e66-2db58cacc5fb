// src/components/Navbar.js
import React from 'react';
import styled from 'styled-components';

const NavbarContainer = styled.nav`
  position: sticky;
  top: 0;
  background: #fff;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
`;

const NavList = styled.ul`
  list-style: none;
  display: flex;
  justify-content: space-around;
`;

const NavItem = styled.li`
  margin: 0 1rem;
`;

const NavLink = styled.a`
  text-decoration: none;
  color: #333;
  font-weight: bold;
`;

const Navbar = () => {
  return (
    <NavbarContainer>
      <NavList>
        <NavItem><NavLink href="/">Home</NavLink></NavItem>
        <NavItem><NavLink href="/about">About</NavLink></NavItem>
        <NavItem><NavLink href="/courses">Courses</NavLink></NavItem>
        <NavItem><NavLink href="/contact">Contact</NavLink></NavItem>
      </NavList>
    </NavbarContainer>
  );
};

export default Navbar;