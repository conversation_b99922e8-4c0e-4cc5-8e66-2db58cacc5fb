// src/components/Footer.js
import React from 'react';
import styled from 'styled-components';

const FooterContainer = styled.footer`
  background: #f8f9fa;
  padding: 2rem;
  text-align: center;
`;

const FooterContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const SocialLinks = styled.div`
  margin-bottom: 1rem;
`;

const QuickLinks = styled.div`
  margin-bottom: 1rem;
`;

const ContactEmail = styled.div`
  margin-bottom: 1rem;
`;

const Footer = () => {
  return (
    <FooterContainer>
      <FooterContent>
        <SocialLinks>
          <a href="https://facebook.com" target="_blank" rel="noopener noreferrer">Facebook</a>
          <a href="https://instagram.com" target="_blank" rel="noopener noreferrer">Instagram</a>
          <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer">LinkedIn</a>
        </SocialLinks>
        <QuickLinks>
          <a href="/">Home</a>
          <a href="/about">About</a>
          <a href="/courses">Courses</a>
          <a href="/contact">Contact</a>
        </QuickLinks>
        <ContactEmail>
          Email: <a href="mailto:<EMAIL>"><EMAIL></a>
        </ContactEmail>
      </FooterContent>
    </FooterContainer>
  );
};

export default Footer;