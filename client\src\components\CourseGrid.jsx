import React from 'react';
import styled from 'styled-components';
import CourseCard from './CourseCard';

const GridContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  padding: 2rem;
`;

const CoursesGrid = ({ courses }) => {
  return (
    <GridContainer>
      {courses.map(course => (
        <CourseCard key={course.id} course={course} />
      ))}
    </GridContainer>
  );
};

export default CoursesGrid;
