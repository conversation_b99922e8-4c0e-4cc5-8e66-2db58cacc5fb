// src/components/Hero3DCarousel.js
import React, { useRef, useEffect } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Swiper from 'swiper';
import 'swiper/css';
import styled from 'styled-components';

const HeroContainer = styled.div`
  position: relative;
  height: 100vh;
`;

const CarouselContainer = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
`;

const Hero3DCarousel = () => {
  const sceneRef = useRef();
  const messages = ["Innovate with Us", "Hands-on Training", "Live Sessions"];

  useEffect(() => {
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    sceneRef.current.appendChild(renderer.domElement);

    const light = new THREE.AmbientLight(0x404040);
    scene.add(light);

    const loader = new GLTFLoader();
    let model;

    // Define params at the top level of the useEffect hook
    const params = { rotation: 0 };

    loader.load('path/to/your/model.glb', (gltf) => {
      model = gltf.scene;
      scene.add(model);

      // Animate the model using GSAP
      gsap.registerPlugin(ScrollTrigger);

      const updateScene = () => {
        model.rotation.y = params.rotation;
        renderer.render(scene, camera);
      };

      gsap.timeline({
        scrollTrigger: {
          trigger: sceneRef.current,
          start: 'top top',
          end: 'bottom bottom',
          scrub: true,
          onUpdate: updateScene,
        },
      })
      .to(params, {
        rotation: Math.PI * 2,
        duration: 1,
        ease: 'none',
      });

      // Resize handling
      window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
      });
    });

    const animate = () => {
      requestAnimationFrame(animate);
      renderer.render(scene, camera);
    };
    animate();

    return () => {
      // Clean up resources
      if (model) {
        model.traverse((child) => {
          if (child.isMesh) {
            child.material.dispose();
            child.geometry.dispose();
          }
        });
      }
      scene.remove(model);
      renderer.dispose();
      gsap.killTweensOf(params); // Ensure params is accessible here
      window.removeEventListener('resize', () => {});
    };
  }, []);

  return (
    <HeroContainer ref={sceneRef}>
      <CarouselContainer>
        <div className="swiper">
          <div className="swiper-wrapper">
            {messages.map((message, index) => (
              <div key={index} className="swiper-slide">{message}</div>
            ))}
          </div>
          <div className="swiper-pagination"></div>
        </div>
      </CarouselContainer>
    </HeroContainer>
  );
};

export default Hero3DCarousel;