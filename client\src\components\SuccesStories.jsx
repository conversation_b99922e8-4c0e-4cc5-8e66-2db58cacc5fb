import React from 'react';
import styled from 'styled-components';
import TestimonialCard from './TestimonialCard';

const StoriesContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  padding: 2rem;
`;

const SuccessStories = ({ testimonials }) => {
  return (
    <StoriesContainer>
      {testimonials.map(testimonial => (
        <TestimonialCard key={testimonial.id} testimonial={testimonial} />
      ))}
    </StoriesContainer>
  );
};

export default SuccessStories;
