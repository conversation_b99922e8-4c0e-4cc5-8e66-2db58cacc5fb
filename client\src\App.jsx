import React from 'react';

import Footer from "./components/Footer";
import Hero3DCarousel from "./components/Hero";
import Navbar from "./components/Navbar";
import SuccessStories from "./components/TestimonialCard";
import WhatsAppCTA from "./components/WhatsappCTA";
import CoursesGrid from './components/CourseGrid';

const AppContainer = 'styled.div'
  'position: relative';
;

const App = () => {
  const courses = [
    { id: 1, thumbnail: 'path/to/image1.jpg', title: 'Course 1', description: 'Description of Course 1', link: '/course1' },
    { id: 2, thumbnail: 'path/to/image2.jpg', title: 'Course 2', description: 'Description of Course 2', link: '/course2' },
    // Add more courses as needed
  ];

  const testimonials = [
    { id: 1, photo: 'path/to/student1.jpg', name: '<PERSON>', quote: 'This course changed my life!' },
    { id: 2, photo: 'path/to/student2.jpg', name: '<PERSON>', quote: 'I highly recommend this course.' },
    // Add more testimonials as needed
  ];

  return (
    <AppContainer>
      <Navbar />
      <Hero3DCarousel />
      < CoursesGrid courses={courses} />
      <SuccessStories testimonials={testimonials} />
      <Footer />
      <WhatsAppCTA />
    </AppContainer>
  );
};

export default App;
