import React from 'react';
import styled from 'styled-components';

const CardContainer = styled.div`
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const Thumbnail = styled.img`
  width: 100%;
  height: auto;
`;

const CardContent = styled.div`
  padding: 1rem;
`;

const Title = styled.h3`
  margin: 0 0 0.5rem;
`;

const Description = styled.p`
  margin: 0 0 1rem;
`;

const LearnMoreLink = styled.a`
  text-decoration: none;
  color: #007bff;
`;

const CourseCard = ({ course }) => {
  return (
    <CardContainer>
      <Thumbnail src={course.thumbnail} alt={course.title} />
      <CardContent>
        <Title>{course.title}</Title>
        <Description>{course.description}</Description>
        <LearnMoreLink href={course.link}>Learn More</LearnMoreLink>
      </CardContent>
    </CardContainer>
  );
};

export default CourseCard;